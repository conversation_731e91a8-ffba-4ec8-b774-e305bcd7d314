import torch

class EvolutionStrategies:
    def __init__(self, iterations, num_agents, env, policy, agents, optimizer, device, sigma, num_env_steps_per_update):
        self.iterations = iterations
        self.num_agents = num_agents
        self.env = env
        self.policy = policy
        self.agents = agents
        self.optimizer = optimizer
        self.device = device
        self.sigma = sigma
        self.num_env_steps = num_env_steps_per_update
        self.init_noise()

    def init_noise(self):
        for name, params in self.agents.named_parameters():
            clean_name = name.replace(".", "_") + "_noise"
            self.agents.register_buffer(clean_name, torch.zeros_like(params).to(self.device))

    """
    def get_flat_params(self):
        flat_params = []
        for p in self.policy.parameters():
            flat_params.append(p.data.reshape(self.num_agents, -1).to(self.device)) #macht einen 2D tensor aus p
        return torch.cat(flat_params, dim=1)  # (num_agents, parameters per agent)

    def set_flat_params(self, flat_params):
        idx = 0
        for p in self.policy.parameters():
            size = p[0].numel() #how many parameters does agent 0 have in layer p
            chunk = flat_params[:, idx: idx + size] #(num_agents, size) layer p for every agent
            p.data.copy_(chunk.view(self.num_agents, *p[0].shape)) #copies layer p from flat_params into layer p at self.policy
            idx += size #move to next layer
    """

    def apply_policy_to_agents(self):
        for policy_param, agnet_param in zip(self.policy.parameters(), self.agents.parameters()):
            agnet_param.data.copy_(policy_param.data)

    def perturbate(self):
        for params, noise in zip(self.agents.parameters(), self.agents.buffers()):
            noise.normal_(0, self.sigma)
            params.data += noise

    def run_rollout(self):
        #---reset envs ---
        obs, _ = self.env.reset()
        obs = obs.to(self.device) #initial observation

        total_rewards = torch.zeros(self.num_agents, device=self.device) #1D tensor with reward per agent
        done = torch.zeros(self.num_agents, dtype=torch.bool, device=self.device)

        #---- rollout ----
        for _ in range(self.num_env_steps):
            # get actions from policy, reacting to observation
            actions = self.agents(obs)

            # step the environment, to let the agents execute the actions
            obs, rewards, dones, extras = self.env.step(actions)

            # (ensure tensors are on the correct device)
            obs = obs.to(self.device)
            rewards = rewards.to(self.device)
            dones = dones.to(self.device)

            # calculate rewards for agents that are still alive
            total_rewards += rewards * (~done)  # ~ to invert the done mask

            # update done mask
            done |= dones

            # break if all agents are done
            if done.all():
                break

        return total_rewards

    def run(self):
        for iteration in range(self.iterations):
            #--- copy policy to agents ---
            self.apply_policy_to_agents()

            #--- perturbate params ---
            self.perturbate()

            # --- rollout ---
            total_rewards = self.run_rollout()

            # normalize rewards
            reward_std = total_rewards.std()
            reward_mean = total_rewards.mean()

            if reward_std < 1e-8 or self.num_agents == 1:
                rewards = total_rewards - reward_mean
            else:
                rewards = (total_rewards - reward_mean) / reward_std

            # --- update policy ---
            self.optimizer.step(rewards)

            print(f"Iteration {iteration+1}/{self.iterations}: "
                f"mean_reward={reward_mean.item():.4f}, "
                f"std_reward={reward_std.item():.4f}")
