import torch

class EvolutionStrategies:
    def __init__(self, iterations, num_agents, env, policy, optimizer, device, sigma, num_env_steps_per_update):
        self.iterations = iterations
        self.num_agents = num_agents
        self.env = env
        self.policy = policy
        self.optimizer = optimizer
        self.device = device
        self.sigma = sigma
        self.num_env_steps = num_env_steps_per_update

    def get_flat_params(self):
        flat_params = []
        for p in self.policy.parameters():
            flat_params.append(p.data.reshape(self.num_agents, -1).to(self.device)) #macht einen 2D tensor aus p
        return torch.cat(flat_params, dim=1)  # (num_agents, parameters per agent)

    def set_flat_params(self, flat_params):
        idx = 0
        for p in self.policy.parameters():
            size = p[0].numel() #how many parameters does agent 0 have in layer p
            chunk = flat_params[:, idx: idx + size] #(num_agents, size) layer p for every agent
            p.data.copy_(chunk.view(self.num_agents, *p[0].shape)) #copies layer p from flat_params into layer p at self.policy
            idx += size #move to next layer

    def sample_noise(self):
        noise_list = []
        for p in self.policy.parameters():
            noise_list.append(torch.randn(self.num_agents, *p.shape, device=self.device)) #creates 3D tensor of noise, matching the shape of the params of each layer
        return noise_list
    
    def perturbate(self, policy, noise_list):
        perturbated_params = []
        for p, n in zip(self.policy.parameters(), noise_list):
            perturbated_params.append(p + self.sigma * n) #shape: (num_agents, *p.shape)
        return perturbated_params

    def run_rollout(self):
        #---reset envs ---
        obs, _ = self.env.reset()
        obs = obs.to(self.device) #initial observation

        total_rewards = torch.zeros(self.num_agents, device=self.device) #1D tensor with reward per agent
        done = torch.zeros(self.num_agents, dtype=torch.bool, device=self.device)

        #---- rollout ----
        with torch.no_grad():
            for _ in range(self.num_env_steps):
                # get actions from policy, reacting to observation
                actions = self.policy(obs)

                # step the environment, to let the agents execute the actions
                obs, rewards, dones, extras = self.env.step(actions)

                # ensure tensors are on the correct device
                obs = obs.to(self.device)
                rewards = rewards.to(self.device)
                dones = dones.to(self.device)

                # calculate rewards for agents that are still alive
                total_rewards += rewards * (~done)  # ~ to invert the done mask

                # update done mask
                done |= dones

                # break if all agents are done
                if done.all():
                    break

        return total_rewards

    def run(self):
        for iteration in range(self.iterations):
            #--- flatten params ---
            base_matrix = self.get_flat_params().to(self.device) #(num_agents, params per agent)
            param_size = base_matrix.shape[1]

            #--- perturbate params ---
            noises = torch.randn(self.num_agents, param_size, device=self.device)

            perturbated_params = base_matrix + self.sigma * noises

            self.set_flat_params(perturbated_params)

            # --- rollout ---
            total_rewards = self.run_rollout()

            self.set_flat_params(base_matrix) # reset params

            # normalize rewards
            reward_std = total_rewards.std()
            if reward_std < 1e-8 or self.num_agents == 1:
                rewards = total_rewards - total_rewards.mean()
            else:
                rewards = (total_rewards - total_rewards.mean()) / reward_std

            # --- update policy ---
            self.optimizer.step(rewards, noises, base_matrix)

            print(f"Iteration {iteration+1}/{self.iterations}: "
                f"mean_reward={total_rewards.mean().item():.2f}, "
                f"std_reward={total_rewards.std().item():.2f}")
