from es import SGD, get_models
import torch

def init_noise(agents, device):
    for name, params in agents.named_parameters():
        clean_name = name.replace(".", "_") + "_noise"
        agents.register_buffer(clean_name, torch.zeros_like(params).to(device))

def perturbate(agents, sigma):
    for params, noise in zip(agents.parameters(), agents.buffers()):
        noise.normal_(0, sigma)
        params += noise

def main() -> None:
    model_config = {
    "input_dim": 10,
    "output_dim": 2,
    "hidden_dim": 64,
    "num_hidden": 2,
    "num_agents": 10
    }
    device = "cuda"
    policy, agents = get_models(model_config["input_dim"], model_config["output_dim"], model_config["hidden_dim"], model_config["num_hidden"], model_config["num_agents"], device)

    optim = SGD(
        policy=policy,
        agents=agents,
        num_agents=10,
        sigma=0.1,
        learning_rate=0.02,
        device=device
    )

    init_noise(agents, device)
    perturbate(agents, 0.1)

    rewards = torch.randn(10, device=device)
    optim.step(rewards)
        
if __name__ == "__main__":
    main()