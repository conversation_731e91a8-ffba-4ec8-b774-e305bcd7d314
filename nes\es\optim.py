import torch

class SGD:
    def __init__(self, policy, agents, learning_rate, sigma, num_agents, device):
        self.policy = policy
        self.agents = agents
        self.learning_rate = learning_rate
        self.sigma = sigma
        self.num_agents = num_agents
        self.device = device
    """
    def get_flat_params(self):
        flat_params = []
        for p in self.policy.parameters():
            flat_params.append(p.data.reshape(self.num_agents, -1).to(self.device)) #macht einen 2D tensor aus p
        return torch.cat(flat_params, dim=1)  # (num_agents, parameters per agent)

    def set_flat_params(self, flat_params):
        idx = 0
        for p in self.policy.parameters():
            size = p[0].numel() #how many parameters does agent 0 have in layer p
            chunk = flat_params[:, idx: idx + size] #(num_agents, size) layer p for every agent
            p.data.copy_(chunk.view(self.num_agents, *p[0].shape)) #copies layer p from flat_params into layer p at self.policy
            idx += size #move to next layer
    """
            
    def step(self, rewards)-> None:

        for params, noise in zip(self.policy.parameters(), self.agents.buffers()):

            # es gradient estimate: ∇_θ J(θ) ≈ (1 / (N σ)) ∑ᵢ Rᵢ εᵢ
            # rewards: [num_agents], noise: [num_agents, ...param_dims]
            rewards_shape = [self.num_agents] + [1] * (noise.dim() - 1)
            rewards_reshaped = rewards.view(rewards_shape)

            gradient = torch.sum(rewards_reshaped * noise, dim=0) / (self.num_agents * self.sigma)

            # update flat_params (gradient ascend)
            params += self.learning_rate * gradient
