import torch.nn as nn
import torch

class Linear(nn.Module):
    def __init__(self, input_dim, output_dim, num_agents):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.num_agents = num_agents
        self.weights = torch.randn(size=(num_agents, input_dim, output_dim)) #3D Tensor aus weight matrizen
        self.bias = torch.randn(size=(num_agents, output_dim))
        
        self.weights = nn.Parameter(self.weights, requires_grad=False)
        self.bias = nn.Parameter(self.bias, requires_grad=False)
        
    def forward(self, x: torch.Tensor):
        x = x.unsqueeze(1)
        #print(x.shape)
        return torch.bmm(x, self.weights).squeeze(1) + self.bias
        
class Model(nn.Module):
    def __init__(self, input_dim, output_dim, hidden_dim, num_hidden, num_agents):
        super().__init__() #inherits from torch.nn.Module, since its a nn
        layers = []
        in_dim = input_dim
        # Building the nn layers
        for _ in range(num_hidden):
            layers.append(Linear(in_dim, hidden_dim, num_agents))
            layers.append(nn.LeakyReLU())
            in_dim = hidden_dim
        layers.append(Linear(hidden_dim, output_dim, num_agents))
        self.net = nn.Sequential(*layers)

    def forward(self, x: torch.Tensor):
        return self.net(x)

model_config = {
    "input_dim": 10,
    "output_dim": 2,
    "hidden_dim": 64,
    "num_hidden": 2,
    "num_agents": 10
}

def get_models(input_dim, output_dim, hidden_dim, num_hidden, num_agents, device):
    policy = Model(input_dim, output_dim, hidden_dim, num_hidden, num_agents).to(device)
    return policy


    
def main():
    device = "cuda"
    model = get_models(model_config["input_dim"], model_config["output_dim"], model_config["hidden_dim"], model_config["num_hidden"], model_config["num_agents"], device)
    x = torch.randn(model_config["num_agents"], model_config["input_dim"]).to(device)
    y = model(x)
    print(y.shape)
    print([p.shape for p in model.parameters()])

if __name__ == "__main__":
    main()


    """
    class Agent:
    def __init__(self, env, base_policy):
        self.env = env  # shared vectorized env
        self.policy = Policy(
            input_dim=base_policy.net[0].in_features,
            output_dim=base_policy.net[-1].out_features,
            hidden_dim=64,
            num_hidden=2
        )
        self.policy.load_state_dict(base_policy.state_dict())
        self.device = next(base_policy.parameters()).device
        self.policy.to(self.device)

    def perturb(self, noise, sigma):
        noise_tensor = torch.tensor(noise, device=next(self.policy.parameters()).device, dtype=torch.float32)
        flat_params = self.policy.get_params()
        self.policy.set_params(flat_params + sigma * noise_tensor)


    def rollout(self, max_steps):
        obs, _ = self.env.reset()
        total_rewards = torch.zeros(self.env.num_envs, device=self.device)
        done = torch.zeros(self.env.num_envs, dtype=torch.bool, device=self.device)

        for _ in range(max_steps):
            obs_t = obs.to(self.device)
            actions = self.policy(obs_t).detach().cpu()
            obs, rewards, dones, _ = self.env.step(actions)

            rewards = torch.tensor(rewards, device=self.device)
            dones = torch.tensor(dones, dtype=torch.bool, device=self.device)

            total_rewards += rewards * (~done)
            done |= dones

            if done.all():
                break

        return total_rewards.cpu().numpy()

    
def get_models(num_agents, input_dim, output_dim, hidden_dim, num_hidden, device, env):
    policy = Policy(input_dim, output_dim, hidden_dim, num_hidden).to(device)
    agents = []
    for _ in range(num_agents):
        agent = Agent(env, policy)
        agents.append(agent)
    return policy, agents
    """